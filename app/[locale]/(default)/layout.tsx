import Footer from "@/components/blocks/footer";
import Header from "@/components/blocks/header";
import { ReactNode } from "react";
import { getLandingPage } from "@/services/page";
import Feedback from "@/components/feedback";


export const runtime = "edge";
export default async function DefaultLayout({
  children,
  params,
}: {
  children: ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;

  try {
    const page = await getLandingPage(locale);

    return (
      <>
        {page.header && <Header header={page.header} />}
        <main className="overflow-x-hidden">{children}</main>
        {page.footer && <Footer footer={page.footer} />}
        {/* <Feedback socialLinks={page.footer?.social?.items} /> */}
      </>
    );
  } catch (error) {
    console.error("Failed to load layout page:", error);

    // 简化的布局
    return (
      <>
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-4">
              <div className="flex items-center">
                <img src="/logo.png" alt="AFT Calculator" className="h-8 w-8 mr-2" />
                <span className="text-xl font-bold text-gray-900">AFT Calculator</span>
              </div>
              <nav className="hidden md:flex space-x-6">
                <a href="/aft-calculator" className="text-gray-600 hover:text-gray-900">Calculator</a>
                <a href="/aft-score-chart" className="text-gray-600 hover:text-gray-900">Score Chart</a>
                <a href="/aft-standards" className="text-gray-600 hover:text-gray-900">Standards</a>
              </nav>
            </div>
          </div>
        </header>
        <main className="overflow-x-hidden">{children}</main>
        <footer className="bg-gray-50 border-t">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="text-center text-gray-600">
              <p>&copy; 2025 AFT Calculator. All rights reserved.</p>
            </div>
          </div>
        </footer>
      </>
    );
  }
}
