import CTA from "@/components/blocks/cta";
import FAQ from "@/components/blocks/faq";
import Feature from "@/components/blocks/feature";
import Hero from "@/components/blocks/hero";
import { getLandingPage } from "@/services/page";


export const runtime = "edge";
export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}`;
  }

  return {
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function LandingPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;

  try {
    const page = await getLandingPage(locale);

    return (
      <>
        {page.hero && <Hero hero={page.hero} />}
        {page.feature && <Feature section={page.feature} />}
        {page.faq && <FAQ section={page.faq} />}
        {page.cta && <CTA section={page.cta} />}
      </>
    );
  } catch (error) {
    console.error("Failed to load landing page:", error);

    // 临时的简化页面
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md mx-auto text-center p-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            AFT Calculator 2025
          </h1>
          <p className="text-gray-600 mb-6">
            Free Army Fitness Test Calculator with accurate scoring based on 2025 standards.
          </p>
          <div className="space-y-3">
            <a
              href="/aft-calculator"
              className="block w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition-colors"
            >
              Start AFT Calculator
            </a>
            <a
              href="/aft-score-chart"
              className="block w-full bg-gray-200 text-gray-800 py-2 px-4 rounded hover:bg-gray-300 transition-colors"
            >
              AFT Score Chart 2025
            </a>
            <a
              href="/api/health"
              className="block w-full bg-green-200 text-green-800 py-2 px-4 rounded hover:bg-green-300 transition-colors"
            >
              Check System Health
            </a>
          </div>
          <p className="text-sm text-gray-500 mt-4">
            Environment: {process.env.NODE_ENV || 'unknown'}
          </p>
        </div>
      </div>
    );
  }
}
