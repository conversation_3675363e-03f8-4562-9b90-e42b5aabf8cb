import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Star,
  Calculator,
  Zap,
  Shield,
  Apple
} from 'lucide-react';
import type { Metadata } from 'next';
import QRCodeDownload from '@/components/download/QRCodeDownload';
import DownloadButton from '@/components/download/DownloadButton';

export const runtime = "edge";

export const metadata: Metadata = {
  title: 'Download AFT Calculator iOS App - Official Army Fitness Test Mobile App 2025',
  description: 'Download the official AFT Calculator iOS app for iPhone. Calculate all 5 AFT events (MDL, HRP, SDC, PLK, 2MR) with 2025 Army fitness test standards. Requires iOS 16.6 or later.',
  keywords: 'AFT Calculator iOS app, Army Fitness Test mobile app, AFT app download, iPhone AFT calculator, AFT Score Chart 2025, AFT Standards app, military fitness calculator',
  openGraph: {
    title: 'AFT Calculator iOS App - Download Now',
    description: 'Official AFT Calculator mobile app for iPhone. Calculate all 5 AFT events with 2025 Army fitness test standards. Requires iOS 16.6 or later.',
    type: 'website',
    url: 'https://aftcalculator.app/download',
    images: [
      {
        url: 'https://aftcalculator.app/og-download.jpg',
        width: 1200,
        height: 630,
        alt: 'AFT Calculator iOS App Download',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'AFT Calculator iOS App - Download Now',
    description: 'Official AFT Calculator mobile app for iPhone. Calculate all 5 AFT events with 2025 Army fitness test standards. Requires iOS 16.6 or later.',
    images: ['https://aftcalculator.app/og-download.jpg'],
  },
};

const appStoreUrl = process.env.NEXT_PUBLIC_IOS_APP_URL || 'https://apps.apple.com/app/id6746973298';

const features = [
  {
    icon: Calculator,
    title: 'Complete AFT Calculator',
    description: 'Calculate all 5 AFT events: MDL, HRP, SDC, PLK, and 2MR with official 2025 Army fitness test standards'
  },
  {
    icon: Zap,
    title: 'Instant Results',
    description: 'Get immediate AFT scores and performance feedback with real-time calculations and progress tracking'
  },
  {
    icon: Shield,
    title: 'Offline Capability',
    description: 'Calculate AFT scores without internet connection - perfect for field training and remote locations'
  },
  {
    icon: Star,
    title: 'Comprehensive Standards',
    description: 'Trusted by military personnel worldwide, featuring complete AFT Score Chart 2025 and current standards'
  }
];

export default function DownloadPage() {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "MobileApplication",
    "name": "AFT Calculator",
    "description": "Official AFT Calculator mobile app for iPhone. Calculate all 5 AFT events with 2025 Army fitness test standards. Requires iOS 16.6 or later.",
    "applicationCategory": "HealthApplication",
    "operatingSystem": "iOS 16.6",
    "downloadUrl": appStoreUrl,
    "screenshot": "https://aftcalculator.app/app-screenshots.jpg",
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.9",
      "ratingCount": "1000"
    }
  };

  return (
    <div className="bg-white">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      {/* Hero Section */}
      <section className="bg-gradient-to-b from-gray-50 to-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="mb-8">
              <Badge className="mb-4 bg-[#FFCC01] text-black border-[#FFCC01]">
                <Apple className="w-4 h-4 mr-1" />
                iOS App Available
              </Badge>
              <h1 className="text-4xl md:text-6xl font-bold mb-6 text-black">
                AFT Calculator
                <span className="text-[#FFCC01]"> iOS App</span>
              </h1>
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                Professional AFT Calculator mobile app for iPhone <br />
                Calculate all 5 AFT events with 2025 Army fitness test standards
              </p>
            </div>

            {/* App Store Button and QR Code */}
            <QRCodeDownload appStoreUrl={appStoreUrl} />
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-black">
                Why Choose Our <span className="text-[#FFCC01]">AFT Calculator App</span>
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
                The most comprehensive AFT Calculator app featuring all 5 Army fitness test events, official AFT Score Chart 2025, and complete AFT Standards for military personnel
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-12 max-w-5xl mx-auto">
              {features.map((feature, index) => {
                const Icon = feature.icon;
                return (
                  <Card key={index} className="border-gray-200 hover:border-[#FFCC01]/50 transition-all duration-300 hover:shadow-xl hover:scale-105 h-full group">
                    <CardHeader className="text-center pb-4">
                      <div className="mx-auto mb-4 w-16 h-16 bg-[#FFCC01]/10 group-hover:bg-[#FFCC01]/20 rounded-xl flex items-center justify-center transition-all duration-300">
                        <Icon className="w-8 h-8 text-[#FFCC01] group-hover:scale-110 transition-transform duration-300" />
                      </div>
                      <CardTitle className="text-xl font-semibold text-black mb-2 group-hover:text-[#FFCC01] transition-colors duration-300">
                        {feature.title}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <CardDescription className="text-gray-600 text-center leading-relaxed">
                        {feature.description}
                      </CardDescription>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        </div>
      </section>

      {/* App Download Section */}
      <section className="py-20 bg-gradient-to-b from-white to-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-8 text-black">
              Download <span className="text-[#FFCC01]">AFT Calculator</span> App Now
            </h2>
            <p className="text-xl text-gray-600 mb-12 max-w-5xl mx-auto leading-relaxed">
              Get the official AFT Calculator iOS app for iPhone. Calculate all 5 AFT events (MDL, HRP, SDC, PLK, 2MR) with 2025 Army fitness test standards anywhere, anytime. Requires iOS 16.6 or later.
            </p>

            <div className="flex flex-col sm:flex-row items-center justify-center gap-8">
              <DownloadButton />

              <div className="flex items-center space-x-2 text-gray-600">
                <Star className="w-5 h-5 text-[#FFCC01] fill-current" />
                <Star className="w-5 h-5 text-[#FFCC01] fill-current" />
                <Star className="w-5 h-5 text-[#FFCC01] fill-current" />
                <Star className="w-5 h-5 text-[#FFCC01] fill-current" />
                <Star className="w-5 h-5 text-[#FFCC01] fill-current" />
                <span className="ml-2 text-sm">Professionally Recommended</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
