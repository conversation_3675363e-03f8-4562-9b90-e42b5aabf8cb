import { Metadata } from "next";
import AFTScoreChartComponent from "@/components/aft/score-chart";

export async function generateMetadata(): Promise<Metadata> {
  return {
    title: "AFT Score Chart 2025 - Army Fitness Test Scoring Tables",
    description: "View the official AFT Score Chart 2025 with complete scoring tables for all Army Fitness Test events. Find your score requirements organized by age group and gender.",
  };
}

export default function AFTScoreChartPage() {

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            AFT Score Chart 2025 - Army Fitness Test Scoring Tables
          </h1>
          <p className="text-xl text-gray-600 mb-6">
            View the official AFT Score Chart 2025 with complete scoring tables for all Army Fitness Test events. Find your score requirements for MDL, HRP, SDC, PLK, and 2MR organized by age group and gender.
          </p>
        </div>
        
        <AFTScoreChartComponent />
        
        <div className="mt-12 grid md:grid-cols-2 gap-8">
          <div className="bg-green-50 rounded-lg p-6">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              Understanding AFT Scores - 5 Events
            </h2>
            <ul className="space-y-2 text-gray-600">
              <li>• AFT includes 5 events: MDL, HRP, SDC, PLK, and 2MR</li>
              <li>• Scores are based on age and gender for each event</li>
              <li>• Maximum score is 100 points per event (500 total)</li>
              <li>• Minimum passing score varies by age group and event</li>
              <li>• Total AFT score is sum of all 5 event scores</li>
            </ul>
          </div>
          
          <div className="bg-blue-50 rounded-lg p-6">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              Score Categories
            </h2>
            <div className="space-y-2 text-gray-600">
              <div className="flex justify-between">
                <span>Excellent:</span>
                <span className="font-medium">90-100 points</span>
              </div>
              <div className="flex justify-between">
                <span>Good:</span>
                <span className="font-medium">80-89 points</span>
              </div>
              <div className="flex justify-between">
                <span>Satisfactory:</span>
                <span className="font-medium">70-79 points</span>
              </div>
              <div className="flex justify-between">
                <span>Needs Improvement:</span>
                <span className="font-medium">60-69 points</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
