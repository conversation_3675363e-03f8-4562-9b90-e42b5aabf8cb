import { respData, respErr } from "@/lib/resp";

export const runtime = "edge";

export async function GET() {
  try {
    const healthCheck = {
      status: "ok",
      timestamp: new Date().toISOString(),
      environment: {
        NODE_ENV: process.env.NODE_ENV,
        NEXT_PUBLIC_WEB_URL: process.env.NEXT_PUBLIC_WEB_URL,
        NEXT_PUBLIC_PROJECT_NAME: process.env.NEXT_PUBLIC_PROJECT_NAME,
        hasSupabaseUrl: !!process.env.SUPABASE_URL,
        hasSupabaseKey: !!process.env.SUPABASE_ANON_KEY,
        supabaseUrlPrefix: process.env.SUPABASE_URL ? process.env.SUPABASE_URL.substring(0, 20) + "..." : "missing",
      },
      runtime: "edge",
      cloudflare: {
        cf: typeof (globalThis as any).cf !== 'undefined' ? 'available' : 'not available',
      }
    };

    return respData(healthCheck);
  } catch (error) {
    console.error("Health check failed:", error);
    return respErr("Health check failed: " + (error as Error).message);
  }
}
