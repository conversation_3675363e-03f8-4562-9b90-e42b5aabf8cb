# Cloudflare Pages 部署指南

本指南将帮助您将 AFT Calculator App 部署到 Cloudflare Pages。

## 项目概述

- **项目类型**: Next.js 15.2.3 应用程序
- **运行时**: Edge Runtime (适配 Cloudflare Pages)
- **构建工具**: @cloudflare/next-on-pages
- **数据库**: Supabase
- **认证**: NextAuth.js
- **国际化**: next-intl

## 前置要求

1. Cloudflare 账户
2. GitHub/GitLab 仓库（包含项目代码）
3. 已配置的 Supabase 项目
4. 必要的第三方服务 API 密钥

## 部署步骤

### 1. 准备代码仓库

确保您的代码已推送到 Git 仓库，并包含以下关键文件：
- `wrangler.toml` - Cloudflare Pages 配置
- `package.json` - 包含 Cloudflare 构建脚本
- `next.config.mjs` - Next.js 配置（已优化）
- `public/_redirects` - 路由重定向规则

### 2. 在 Cloudflare Pages 中创建项目

1. 登录 [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. 选择 **Pages** > **Create a project**
3. 选择 **Connect to Git**
4. 授权并选择您的仓库
5. 配置构建设置：
   - **Framework preset**: Next.js
   - **Build command**: `pnpm cf:build`
   - **Build output directory**: `.vercel/output/static`
   - **Root directory**: `/` (如果项目在根目录)

### 2.1. 配置兼容性标志（重要！）

在创建项目后，必须配置 Node.js 兼容性标志：

1. 进入项目的 **Settings** 标签
2. 找到 **Functions** 部分
3. 在 **Compatibility flags** 中添加：`nodejs_compat`
4. 在 **Compatibility date** 中设置：`2024-12-01`
5. 保存设置

> ⚠️ **重要提示**: 如果不设置 `nodejs_compat` 标志，页面将显示 "Node.JS Compatibility Error" 错误。

### 3. 配置环境变量

在 Cloudflare Pages 项目设置中添加以下环境变量：

#### 基础配置
```
NEXT_PUBLIC_WEB_URL=https://your-domain.pages.dev
NEXT_PUBLIC_PROJECT_NAME=AftCalculator
NEXT_PUBLIC_DEFAULT_THEME=light
NEXT_PUBLIC_LOCALE_DETECTION=false
```

#### 数据库配置 (Supabase)
```
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

#### 认证配置 (NextAuth.js)
```
AUTH_SECRET=your_auth_secret_key
AUTH_GOOGLE_ID=your_google_oauth_client_id
AUTH_GOOGLE_SECRET=your_google_oauth_client_secret
NEXT_PUBLIC_AUTH_GOOGLE_ID=your_google_oauth_client_id
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED=true
NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED=false
AUTH_GITHUB_ID=your_github_oauth_app_id
AUTH_GITHUB_SECRET=your_github_oauth_app_secret
NEXT_PUBLIC_AUTH_GITHUB_ENABLED=false
```

#### 分析服务配置
```
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=your_ga_tracking_id
NEXT_PUBLIC_MICROSOFT_CLARITY_ID=your_clarity_project_id
NEXT_PUBLIC_OPENPANEL_CLIENT_ID=your_openpanel_client_id
```

#### 支付配置 (Stripe)
```
STRIPE_PUBLIC_KEY=your_stripe_publishable_key
STRIPE_PRIVATE_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret
NEXT_PUBLIC_PAY_SUCCESS_URL=https://your-domain.pages.dev/my-orders
NEXT_PUBLIC_PAY_FAIL_URL=https://your-domain.pages.dev/#pricing
NEXT_PUBLIC_PAY_CANCEL_URL=https://your-domain.pages.dev/#pricing
```

#### 存储配置 (AWS S3 兼容)
```
STORAGE_ENDPOINT=your_s3_endpoint
STORAGE_REGION=your_s3_region
STORAGE_ACCESS_KEY=your_s3_access_key
STORAGE_SECRET_KEY=your_s3_secret_key
STORAGE_BUCKET=your_s3_bucket_name
STORAGE_DOMAIN=your_s3_domain
```

#### 应用商店链接
```
NEXT_PUBLIC_IOS_APP_URL=https://apps.apple.com/app/id6746973298
NEXT_PUBLIC_ANDROID_APP_URL=https://play.google.com/store/apps/details?id=com.aftcalculator.app
```

#### 管理员配置
```
ADMIN_EMAILS=<EMAIL>,<EMAIL>
```

### 4. 自定义域名配置（可选）

1. 在 Cloudflare Pages 项目中选择 **Custom domains**
2. 添加您的域名
3. 按照提示配置 DNS 记录
4. 更新环境变量中的 `NEXT_PUBLIC_WEB_URL`

### 5. 部署验证

1. 触发部署（推送代码或手动触发）
2. 检查构建日志确保无错误
3. 访问部署的网站验证功能：
   - 首页加载正常
   - AFT 计算器功能正常
   - 用户认证流程正常
   - API 路由响应正常

## 本地开发和测试

### 本地构建测试
```bash
# 安装依赖
pnpm install

# 本地构建测试
pnpm cf:build

# 本地预览
pnpm cf:preview
```

### 本地开发
```bash
# 开发模式
pnpm dev
```

## 故障排除

### 常见问题

1. **Node.JS Compatibility Error**
   - 错误信息：`no nodejs_compat compatibility flag set`
   - 解决方案：在 Cloudflare Pages 项目设置中添加 `nodejs_compat` 兼容性标志
   - 位置：Settings > Functions > Compatibility flags

2. **Internal Server Error**
   - 常见原因：环境变量未配置或配置错误
   - 解决方案：
     - 检查 Supabase 环境变量是否正确设置（SUPABASE_URL, SUPABASE_ANON_KEY）
     - 访问 `/api/health` 端点检查系统状态
     - 查看 Cloudflare Pages Functions 日志获取详细错误信息
   - 调试步骤：
     1. 确保所有必需的环境变量都已设置
     2. 验证 Supabase 项目是否正常运行
     3. 检查 NextAuth.js 配置（AUTH_SECRET 等）

3. **构建失败 - Edge Runtime 错误**
   - 确保所有页面和 API 路由都有 `export const runtime = "edge";`
   - 客户端组件不应该有 Edge Runtime 配置

3. **环境变量未生效**
   - 检查变量名是否正确
   - 重新部署项目以应用新的环境变量

4. **路由 404 错误**
   - 检查 `public/_redirects` 文件是否正确
   - 确保 SPA 路由重定向规则正确

5. **数据库连接失败**
   - 验证 Supabase 配置是否正确
   - 检查数据库表是否已创建（参考 `data/install.sql`）

### 调试技巧

1. 查看 Cloudflare Pages 构建日志
2. 使用浏览器开发者工具检查网络请求
3. 检查 Cloudflare Pages Functions 日志

## 性能优化建议

1. 启用 Cloudflare 的缓存和 CDN 功能
2. 配置适当的缓存头
3. 使用 Cloudflare Analytics 监控性能
4. 定期更新依赖项

## 安全注意事项

1. 定期轮换 API 密钥和秘钥
2. 使用强密码和 2FA
3. 限制管理员邮箱列表
4. 定期审查访问日志

## 支持和维护

- 定期检查 Cloudflare Pages 的更新和新功能
- 监控应用性能和错误日志
- 保持依赖项更新
- 备份重要数据和配置

---

部署完成后，您的 AFT Calculator App 将在 Cloudflare Pages 上运行，享受全球 CDN 加速和 Edge Runtime 的高性能。
