import { createClient } from "@supabase/supabase-js";

export function getSupabaseClient() {
  const supabaseUrl = process.env.SUPABASE_URL || "";

  let supabaseKey = process.env.SUPABASE_ANON_KEY || "";
  if (process.env.SUPABASE_SERVICE_ROLE_KEY) {
    supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  }

  if (!supabaseUrl || !supabaseKey) {
    console.error("Supabase configuration missing:", {
      hasUrl: !!supabaseUrl,
      hasKey: !!supabaseKey,
      url: supabaseUrl ? supabaseUrl.substring(0, 20) + "..." : "missing",
    });
    throw new Error("Supabase URL or key is not set. Please configure SUPABASE_URL and SUPABASE_ANON_KEY environment variables.");
  }

  const client = createClient(supabaseUrl, supabaseKey);

  return client;
}
