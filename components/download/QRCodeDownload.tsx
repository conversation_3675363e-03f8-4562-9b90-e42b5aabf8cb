'use client';

import React from 'react';
import { QrCode } from 'lucide-react';
import QRCode from 'react-qr-code';

interface QRCodeDownloadProps {
  appStoreUrl: string;
}

export default function QRCodeDownload({ appStoreUrl }: QRCodeDownloadProps) {
  const handleDownloadClick = () => {
    const iosAppUrl = process.env.NEXT_PUBLIC_IOS_APP_URL || 'https://apps.apple.com/app/id6746973298';
    window.open(iosAppUrl, '_blank', 'noopener,noreferrer');
  };

  return (
    <div className="flex flex-col md:flex-row items-center justify-center gap-8 mb-12">
      {/* App Store Button */}
      <div className="flex flex-col items-center space-y-4">
        <div
          onClick={handleDownloadClick}
          className="transition-all duration-300 hover:scale-105 cursor-pointer"
        >
          <img
            src="/imgs/download-on-the-app-store.svg"
            alt="Download on the App Store"
            className="h-16 w-auto hover:opacity-90 transition-opacity"
          />
        </div>
        <div className="text-center">
          <p className="text-sm text-gray-600 font-medium">Compatible with iPhone</p>
          <p className="text-xs text-gray-500">iOS 16.6 or later</p>
        </div>
      </div>

      {/* QR Code */}
      <div className="flex flex-col items-center space-y-4">
        <div
          onClick={handleDownloadClick}
          className="bg-white p-6 rounded-xl shadow-lg border-2 border-[#FFCC01]/20 hover:border-[#FFCC01]/40 transition-all duration-300 cursor-pointer"
        >
          <QRCode
            value={appStoreUrl}
            size={140}
            style={{ height: "auto", maxWidth: "100%", width: "100%" }}
            viewBox={`0 0 140 140`}
            fgColor="#000000"
            bgColor="#ffffff"
          />
        </div>
        <div className="text-center">
          <div className="flex items-center justify-center mb-1">
            <QrCode className="w-4 h-4 mr-1 text-[#FFCC01]" />
            <span className="text-sm font-medium text-gray-700">Scan QR Code</span>
          </div>
          <p className="text-xs text-gray-500">Point your camera at the QR code</p>
        </div>
      </div>
    </div>
  );
}
