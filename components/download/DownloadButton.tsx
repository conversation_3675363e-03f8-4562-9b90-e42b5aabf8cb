'use client';

import React from 'react';

export default function DownloadButton(): React.JSX.Element {
  const handleDownloadClick = () => {
    const iosAppUrl = process.env.NEXT_PUBLIC_IOS_APP_URL || 'https://apps.apple.com/app/id6746973298';
    window.open(iosAppUrl, '_blank', 'noopener,noreferrer');
  };

  return (
    <div
      onClick={handleDownloadClick}
      className="inline-block transition-all duration-300 hover:scale-105 cursor-pointer"
    >
      <img
        src="/imgs/download-on-the-app-store.svg"
        alt="Download on the App Store"
        className="h-14 w-auto hover:opacity-90 transition-opacity"
      />
    </div>
  );
}
