import { But<PERSON> } from "@/components/ui/button";
import Icon from "@/components/icon";
import Link from "next/link";
import { Section as SectionType } from "@/types/blocks/section";

export default function CTA({ section }: { section: SectionType }) {
  if (section.disabled) {
    return null;
  }

  return (
    <section id={section.name} className="py-16">
      <div className="px-8">
        <div className='flex items-center justify-center rounded-2xl  bg-[url("/imgs/masks/circle.svg")] bg-cover bg-center px-8 py-12 text-center md:p-16'>
          <div className="mx-auto max-w-(--breakpoint-md)">
            <h2 className="mb-4 text-balance text-3xl font-semibold md:text-5xl">
              {section.title}
            </h2>
            <p className="text-muted-foreground md:text-lg">
              {section.description}
            </p>
            {section.buttons && (
              <div className="mt-8 flex flex-col justify-center gap-4 sm:flex-row">
                {section.buttons.map((item, idx) => {
                  // Handle image variant buttons
                  if (item.variant === "image" && item.image?.src) {
                    return (
                      <Link
                        key={idx}
                        href={item.url || ""}
                        target={item.target}
                        className="flex items-center justify-center"
                      >
                        <img
                          src={item.image.src}
                          alt={item.image.alt || item.title || "Download button"}
                          className="h-14 w-auto hover:opacity-80 transition-opacity cursor-pointer"
                        />
                      </Link>
                    );
                  }

                  // Handle regular buttons
                  return (
                    <Button key={idx} variant={item.variant === "image" ? "default" : (item.variant || "default")}>
                      <Link
                        href={item.url || ""}
                        target={item.target}
                        className="flex items-center justify-center gap-1"
                      >
                        {item.title}
                        {item.icon && (
                          <Icon name={item.icon as string} className="size-6" />
                        )}
                      </Link>
                    </Button>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
}
