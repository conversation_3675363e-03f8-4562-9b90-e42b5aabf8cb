# API routes should be handled by the server
/api/* /api/:splat 200

# Static assets
/favicon.ico /favicon.ico 200
/robots.txt /robots.txt 200
/sitemap.xml /sitemap.xml 200
/logo.png /logo.png 200
/imgs/* /imgs/:splat 200

# Legal pages (static)
/privacy-policy /privacy-policy.html 200
/terms-of-service /terms-of-service.html 200

# Locale-specific routes - redirect to index.html for client-side routing
/en/* /index.html 200
/en-US/* /index.html 200

# Catch-all for SPA routing - redirect all other routes to index.html
/* /index.html 200
